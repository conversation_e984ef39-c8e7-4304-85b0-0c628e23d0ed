<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/functions.php';

// 检查登录状态
require_login();

$current_page = $_GET['page'] ?? 'dashboard';

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
$stmt->execute([$_SESSION['admin_user_id']]);
$admin_user = $stmt->fetch();

if (!$admin_user) {
    logout();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI客服系统 - 管理后台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/style.css?v=<?php echo time(); ?>">
    <!-- 强制刷新CSS缓存，确保最新样式生效 -->
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>
    
    <!-- 主内容包装器 -->
    <div class="main-wrapper">
        <!-- 主头部 -->
        <header class="main-header">
            <div class="header-left">
                <h1 class="header-title">
                    <i class="fas fa-robot"></i>
                    小梅花AI客服系统
                </h1>
            </div>
            <div class="header-right">
                <div class="header-user">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <span class="user-role">系统管理员</span>
                    </div>
                    <i class="fas fa-user-circle user-avatar"></i>
                    
                    <div class="user-dropdown">
                        <a href="index.php?page=settings" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="logout.php" class="dropdown-item logout-item" onclick="return confirm('确定要退出登录吗？')">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <?php
            switch ($current_page) {
                case 'dashboard':
                    include 'templates/dashboard.php';
                    break;
                case 'homepage':
                    include 'templates/homepage.php';
                    break;
                case 'keys':
                    include 'templates/keys.php';
                    break;
                case 'scripts':
                    include 'templates/scripts.php';
                    break;

                case 'users':
                    include 'templates/users.php';
                    break;
                case 'analytics':
                    include 'templates/analytics.php';
                    break;
                case 'settings':
                    include 'templates/settings.php';
                    break;
                default:
                    include 'templates/dashboard.php';
                    break;
            }
            ?>
        </main>
    </div>
    
    <!-- 侧边栏 -->
    <aside class="sidebar" id="sidebar">
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="?page=dashboard" class="nav-link <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span class="nav-text">总数据</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=homepage" class="nav-link <?php echo $current_page === 'homepage' ? 'active' : ''; ?>" data-page="homepage">
                        <i class="fas fa-edit"></i>
                        <span class="nav-text">首页编辑器</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=keys" class="nav-link <?php echo $current_page === 'keys' ? 'active' : ''; ?>" data-page="keys">
                        <i class="fas fa-key"></i>
                        <span class="nav-text">卡密管理</span>
                    </a>
                </li>
                <li class="nav-item has-submenu">
                    <a href="?page=scripts" class="nav-link" data-page="scripts">
                        <i class="fas fa-code"></i>
                        <span class="nav-text">脚本管理</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </a>
                    <ul class="submenu">
                        <li class="submenu-item">
                            <a href="?page=scripts" class="submenu-link <?php echo $current_page === 'scripts' ? 'active' : ''; ?>" data-page="scripts">
                                <i class="fas fa-list"></i>
                                <span class="submenu-text">脚本列表</span>
                            </a>
                        </li>

                    </ul>
                </li>
                <li class="nav-item">
                    <a href="?page=users" class="nav-link <?php echo $current_page === 'users' ? 'active' : ''; ?>" data-page="users">
                        <i class="fas fa-users"></i>
                        <span class="nav-text">用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=analytics" class="nav-link <?php echo $current_page === 'analytics' ? 'active' : ''; ?>" data-page="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span class="nav-text">数据分析</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="?page=settings" class="nav-link <?php echo $current_page === 'settings' ? 'active' : ''; ?>" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span class="nav-text">系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="version-info">
                <small>测试版本 v1.0</small>
            </div>
        </div>
    </aside>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，开始初始化...');
            initializeNavigation();
            initializeSubmenu();
            initializeParticles();
            initializeResponsive();
        });

        // 初始化导航
        function initializeNavigation() {
            console.log('📋 初始化导航...');
            const navLinks = document.querySelectorAll('.nav-link, .submenu-link');
            const currentPage = new URLSearchParams(window.location.search).get('page') || 'dashboard';
            console.log('当前页面:', currentPage);
            
            navLinks.forEach(link => {
                const page = link.getAttribute('data-page');
                if (page === currentPage) {
                    link.classList.add('active');
                    console.log('激活导航项:', page);
                } else {
                    link.classList.remove('active');
                }
            });
        }

        // 初始化二级菜单功能
        function initializeSubmenu() {
            console.log('🔧 初始化二级菜单...');
            const hasSubmenuItems = document.querySelectorAll('.nav-item.has-submenu');
            console.log('找到二级菜单项数量:', hasSubmenuItems.length);
            
            hasSubmenuItems.forEach((item, index) => {
                const navLink = item.querySelector('.nav-link');
                const submenu = item.querySelector('.submenu');
                
                console.log(`设置菜单项 ${index + 1}:`, navLink ? navLink.textContent.trim() : 'null');
                
                if (navLink && submenu) {
                    navLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🔄 切换二级菜单状态');
                        
                        // 切换当前菜单的展开状态
                        const isActive = item.classList.contains('active');
                        
                        if (isActive) {
                            item.classList.remove('active');
                            console.log('📂 收起菜单');
                        } else {
                            item.classList.add('active');
                            console.log('📁 展开菜单');
                        }
                        
                        // 关闭其他展开的子菜单
                        hasSubmenuItems.forEach(otherItem => {
                            if (otherItem !== item) {
                                otherItem.classList.remove('active');
                            }
                        });
                    });
                } else {
                    console.warn('⚠️ 菜单项缺少必要元素:', {navLink, submenu});
                }
            });
            
            // 不再自动展开二级菜单，让用户手动点击控制
            console.log('🎯 二级菜单需要用户手动点击展开');
            
            console.log('✅ 二级菜单初始化完成');
        }

        // 初始化响应式功能
        function initializeResponsive() {
            // 监听窗口大小变化
            window.addEventListener('resize', handleResize);
            handleResize(); // 初始化时执行一次
        }

        // 处理窗口大小变化
        function handleResize() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');
            
            if (window.innerWidth > 768) {
                // 桌面端：显示侧边栏，隐藏遮罩
                if (sidebar) sidebar.classList.remove('mobile-open');
                if (overlay) overlay.classList.remove('active');
            } else {
                // 移动端：确保侧边栏默认隐藏
                if (sidebar) sidebar.classList.remove('mobile-open');
                if (overlay) overlay.classList.remove('active');
            }
        }

        // 初始化粒子背景
        function initializeParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;
            
            // 清空现有粒子
            particlesContainer.innerHTML = '';
            
            // 根据设备调整粒子数量
            const particleCount = window.innerWidth < 768 ? 30 : 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机位置和大小
                const size = Math.random() * 4 + 2;
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                const duration = Math.random() * 20 + 10;
                const delay = Math.random() * 5;
                
                particle.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 50%;
                    left: ${x}%;
                    top: ${y}%;
                    animation: float ${duration}s ${delay}s infinite ease-in-out;
                    pointer-events: none;
                `;
                
                particlesContainer.appendChild(particle);
            }
        }

        // 头部用户下拉菜单功能
        document.addEventListener('click', function(event) {
            const headerUser = document.querySelector('.header-user');
            const userDropdown = document.querySelector('.user-dropdown');
            
            if (headerUser && userDropdown) {
                if (headerUser.contains(event.target)) {
                    // 如果点击的是下拉菜单内的链接，不切换状态
                    if (!event.target.closest('.dropdown-item')) {
                        userDropdown.classList.toggle('active');
                    }
                } else {
                    userDropdown.classList.remove('active');
                }
            }
        });
        
        // 添加浮动动画CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0%, 100% { transform: translateY(0px); opacity: 0.3; }
                50% { transform: translateY(-20px); opacity: 0.7; }
            }
            
            .particles {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1;
                overflow: hidden;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html> 